import { useEffect, useMemo, useState } from "react";
import Supercluster, {
  Options,
  ClusterProperties,
  PointFeature,
  AnyProps,
  ClusterFeature,
} from "supercluster";
import { useMapViewport } from "./use-map-viewport";
import { Position } from "geojson";

export function useCluster<T extends AnyProps>(
  points: PointFeature<T>[],
  options: Options<T, ClusterProperties>
) {
  const [initialized, setInitialized] = useState(false);
  const clusterer = useMemo(() => new Supercluster(options), [options]);

  useEffect(() => {
    if(!points.length) {
      setInitialized(false);
      return;
    }
    console.log('LOAD', { points: points.length })
    try {
      clusterer.load(points);
      setInitialized(true);
    } catch (error) {
      console.error('Error loading points into clusterer:', error);
      setInitialized(false);
    }
  }, [points, clusterer]);

  const [bbox, zoom] = useMapViewport({ padding: 10 });

  // retrieve the clusters within the current viewport
  const clusters = useMemo(() => {
    // don't try to read clusters before data was loaded into the clusterer (version===0),
    // otherwise getClusters will crash
    if (!clusterer || !initialized) return [];

    // Validate bbox and zoom parameters
    if (!bbox || bbox.length !== 4 || typeof zoom !== 'number' || zoom < 0) {
      console.warn('Invalid bbox or zoom parameters:', { bbox, zoom });
      return [];
    }

    try {
        console.log('Getting clusters with:', { bbox, zoom, initialized });
        return clusterer.getClusters(bbox, zoom);
    } catch(e) {
        console.error('Error getting clusters:', e, { bbox, zoom, initialized });
        return []
    }

  }, [initialized, clusterer, bbox, zoom]);

  return [clusters] as const;
}

export const pointFeature = <T>(
  id: string,
  position: Position,
  properties: T
): PointFeature<T> => ({
  type: "Feature",
  id,
  properties,
  geometry: {
    type: "Point",
    coordinates: position,
  },
});

export function isCluster<T extends AnyProps>(item: PointFeature<T> | ClusterFeature<ClusterProperties>): item is ClusterFeature<ClusterProperties> {
    return item.properties.cluster
}
