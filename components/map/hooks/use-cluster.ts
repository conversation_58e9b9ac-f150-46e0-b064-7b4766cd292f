import { useEffect, useMemo, useState } from "react";
import Supercluster, {
  Options,
  ClusterProperties,
  PointFeature,
  AnyProps,
  ClusterFeature,
} from "supercluster";
import { useMapViewport } from "./use-map-viewport";
import { Position } from "geojson";

export function useCluster<T extends AnyProps>(
  points: PointFeature<T>[],
  options: Options<T, ClusterProperties>
) {
  const [initialized, setInitialized] = useState(false);

  // Create a stable clusterer instance with safe defaults
  const clusterer = useMemo(() => {
    console.log('Creating new Supercluster with options:', options);

    // Use safe default options to avoid any configuration issues
    const safeOptions = {
      radius: 40,
      maxZoom: 16,
      minZoom: 0,
      minPoints: 2,
      ...options
    };

    try {
      const cluster = new Supercluster(safeOptions);
      console.log('Supercluster created successfully with options:', safeOptions);
      return cluster;
    } catch (error) {
      console.error('Error creating Supercluster:', error);
      // Return null if creation fails
      return null;
    }
  }, [JSON.stringify(options)]); // Use JSON.stringify for stable comparison

  useEffect(() => {
    if(!clusterer) {
      console.error('Clusterer is null, cannot load points');
      setInitialized(false);
      return;
    }

    if(!points.length) {
      setInitialized(false);
      return;
    }
    console.log('LOAD', { points: points.length, samplePoint: points[0] })

    // Validate points structure
    const invalidPoints = points.filter(point =>
      !point ||
      !point.geometry ||
      !point.geometry.coordinates ||
      !Array.isArray(point.geometry.coordinates) ||
      point.geometry.coordinates.length !== 2 ||
      typeof point.geometry.coordinates[0] !== 'number' ||
      typeof point.geometry.coordinates[1] !== 'number'
    );

    if (invalidPoints.length > 0) {
      console.error('Invalid points found:', invalidPoints);
      setInitialized(false);
      return;
    }

    try {
      clusterer.load(points);
      setInitialized(true);
    } catch (error) {
      console.error('Error loading points into clusterer:', error);
      setInitialized(false);
    }
  }, [points, clusterer]);

  const [bbox, zoom] = useMapViewport({ padding: 10 });

  // retrieve the clusters within the current viewport
  const clusters = useMemo(() => {
    // don't try to read clusters before data was loaded into the clusterer (version===0),
    // otherwise getClusters will crash
    if (!clusterer || !initialized) {
      console.log('Skipping cluster computation:', { clusterer: !!clusterer, initialized });
      return [];
    }

    // Validate bbox and zoom parameters
    if (!bbox || bbox.length !== 4 || typeof zoom !== 'number' || zoom < 0) {
      console.warn('Invalid bbox or zoom parameters:', { bbox, zoom });
      return [];
    }

    try {
        console.log('Getting clusters with:', { bbox, zoom, initialized, maxZoom: options.maxZoom });

        // Ensure zoom is within valid range
        const clampedZoom = Math.max(0, Math.min(zoom, options.maxZoom || 16));
        if (clampedZoom !== zoom) {
          console.warn('Zoom clamped from', zoom, 'to', clampedZoom);
        }

        return clusterer.getClusters(bbox, clampedZoom);
    } catch(e) {
        console.error('Error getting clusters:', e, { bbox, zoom, initialized, stack: e instanceof Error ? e.stack : 'No stack trace' });
        return []
    }

  }, [initialized, clusterer, bbox, zoom]);

  return [clusters] as const;
}

export const pointFeature = <T>(
  id: string,
  position: Position,
  properties: T
): PointFeature<T> => ({
  type: "Feature",
  id,
  properties,
  geometry: {
    type: "Point",
    coordinates: position,
  },
});

export function isCluster<T extends AnyProps>(item: PointFeature<T> | ClusterFeature<ClusterProperties>): item is ClusterFeature<ClusterProperties> {
    return item.properties.cluster
}
