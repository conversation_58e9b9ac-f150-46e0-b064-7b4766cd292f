import { AdvancedMarker, Pin } from "@vis.gl/react-google-maps";
import { Company, CompanyLocation } from "../../../models/company/company";
import { useTheme } from "@mui/material";
import CompanyAvatar from "../../company/CompanyAvatar";

interface CompanyMarkerProps {
  company: Company;
  location: CompanyLocation;
  selected?: boolean;
  onClick?: () => void;
}

export default function CompanyMarker({
  company,
  location,
  selected,
  onClick,
}: CompanyMarkerProps) {
  const theme = useTheme();
  const coordinates = location.coordinates;

  if (!coordinates) return null;

  return (
    <AdvancedMarker
      position={{
        lat: coordinates.latitude,
        lng: coordinates.longitude,
      }}
      onClick={onClick}
    >
      <Pin
        background={
          selected ? theme.palette.success.light : theme.palette.primary.main
        }
        borderColor={
          selected ? theme.palette.background.paper : theme.palette.divider
        }
        scale={selected ? 1.5 : 1}
      >
        <CompanyAvatar
          company={company}
          sx={selected ? { width: 26, height: 26 } : { width: 20, height: 20 }}
        />
      </Pin>
    </AdvancedMarker>
  );
}
