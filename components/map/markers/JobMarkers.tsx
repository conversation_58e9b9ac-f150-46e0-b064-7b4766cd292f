import { <PERSON><PERSON><PERSON><PERSON>, Pin } from "@vis.gl/react-google-maps";
import { Job } from "../../../models/job/job";
import { isCluster, pointFeature, useCluster } from "../hooks/use-cluster";
import { isDefined } from "../../../lib/filter-undefined";
import JobMarker from "./JobMarker";

interface JobMarkersProps {
  jobs: Job[];
  selected?: string[];
  onClick?: (id: string, job: Job) => void;
}

/**
 * Component that displays markers for a list of jobs.
 */
export default function JobMarkers({ jobs }: JobMarkersProps) {
  // Use simple test data first to isolate the issue
  const testPoints = [
    pointFeature('test1', [18.0686, 59.3293], { name: 'Stockholm' }),
    pointFeature('test2', [11.9746, 57.7089], { name: 'Gothenburg' }),
    pointFeature('test3', [13.2000, 55.7047], { name: 'Malmö' })
  ];

  //const points = testPoints;

  // Original code (commented out for testing)
   const points = jobs
       ?.map((job) =>
         job.cache?.customerLocation?.coordinates
           ? pointFeature(job.reference.id, [job.cache.customerLocation.coordinates.longitude, job.cache.customerLocation.coordinates.latitude], { job })
           : null
       )
       .filter(isDefined) ?? [];

  // Use clustering options that allow individual points to show at higher zoom levels
  const [clusters] = useCluster(points, {
    radius: 40,
    maxZoom: 14, // Clusters will break apart into individual points at zoom 15+
    minPoints: 2 // Require at least 2 points to form a cluster
  });

  console.log('Clusters:', clusters);
  //console.log('Current zoom:', map?.getZoom());

  if (!clusters) return null;

  return (
    <>
      {clusters?.map((feature) => {
        const [lng, lat] = feature.geometry.coordinates;
        if (isCluster(feature)) {
          return (<JobClusterMarker key={feature.id} position={{ lat, lng }} />)
        }

        // For test data, just return a simple marker
/*         return (
          <AdvancedMarker
            key={feature.id}
            position={{ lat, lng }}
          >
            <Pin background={'blue'} />
          </AdvancedMarker>
        );
 */
        // Original code (commented out for testing)
         const job = feature.properties.job
         return (
           <JobMarker
             key={feature.id}
             job={job}
             selected={selected?.includes(job.reference.id)}
             onClick={() => onClick?.(job.reference.id, job)}
           />
       );
      })}
    </>
  );
}

function JobClusterMarker({ position }: { position: google.maps.LatLngLiteral }) {
  return (
    <AdvancedMarker
      position={position}
    >
      <Pin>
      </Pin>
    </AdvancedMarker>
  );
}
