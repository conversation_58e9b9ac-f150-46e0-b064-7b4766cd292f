import { <PERSON><PERSON><PERSON><PERSON>, Pin } from "@vis.gl/react-google-maps";
import { Job } from "../../../models/job/job";
import JobMarker from "./JobMarker";
import { isCluster, pointFeature, useCluster } from "../hooks/use-cluster";
import { isDefined } from "../../../lib/filter-undefined";

interface JobMarkersProps {
  jobs: Job[];
  selected?: string[];
  onClick?: (id: string, job: Job) => void;
}

/**
 * Component that displays markers for a list of jobs.
 */
export default function JobMarkers({
  jobs,
  onClick,
  selected,
}: JobMarkersProps) {
  const points = jobs
      ?.map((job) =>
        job.cache?.customerLocation?.coordinates
          ? pointFeature(job.reference.id, [job.cache.customerLocation.coordinates.longitude, job.cache.customerLocation.coordinates.latitude], { job })
          : null
      )
      .filter(isDefined) ?? [];

  const [clusters] = useCluster(points, {
    extent: 256,
    radius: 80,
    maxZoom: 12,
    log: true
  });

  if (!clusters) return null;

  return (
    <>
      {clusters?.map((feature) => {
        const [lng, lat] = feature.geometry.coordinates;
        if (isCluster(feature)) {
          return (<JobClusterMarker key={feature.id} position={{ lat, lng }} />)
        }

        const job = feature.properties.job

        return (
          <JobMarker
            key={feature.id}
            job={job}
            selected={selected?.includes(job.reference.id)}
            onClick={() => onClick?.(job.reference.id, job)}
          />
        );
      })}
    </>
  );
}

function JobClusterMarker({ position }: { position: google.maps.LatLngLiteral }) {
  return (
    <AdvancedMarker
      position={position}
    >
      <Pin>
      </Pin>
    </AdvancedMarker>
  );
}
