import { <PERSON><PERSON>ark<PERSON>, Pin, useMap } from "@vis.gl/react-google-maps";
import { Job } from "../../../models/job/job";
import PartnerAvatar from "../../partners/PartnerAvatar";
import { useTheme } from "@mui/material";
import { MarkerClusterer } from "@googlemaps/markerclusterer";
import { useEffect, useRef } from "react";

interface JobMarkerProps {
  job: Job;
  selected?: boolean;
  onClick?: () => void;
  cluster?: MarkerClusterer;
}

/**
 * Displays a marker for a job.
 */
export default function JobMarker({
  job,
  onClick,
  selected,
  cluster,
}: JobMarkerProps) {
  const theme = useTheme();
  const location = job.cache?.customerLocation?.coordinates;

  const ref = useRef<google.maps.marker.AdvancedMarkerElement>(null);

  const map = useMap()

  useEffect(() => {
    console.log('RENDER', { haveRef: Boolean(ref.current), haveCluster: <PERSON><PERSON><PERSON>(cluster)})

    // Store ref so it can be referenced when unmounting.
    const currentRef = ref.current
    
    if (currentRef) {
      cluster?.addMarker(currentRef);
    }
    return () => {
      console.log('DESTROY', { haveRef: Boolean(ref.current), haveCluster: Boolean(cluster)})
      if (currentRef) {
        cluster?.removeMarker(currentRef);
      }
    };
  }, [cluster, ref.current]);

  if(!location) return null

  return (
    <AdvancedMarker
      onClick={onClick}
      position={{
        lat: location.latitude,
        lng: location.longitude,
      }}
      ref={ref}
    >
      <Pin
        background={
          selected ? theme.palette.error.light : theme.palette.background.paper
        }
        borderColor={
          selected ? theme.palette.background.paper : theme.palette.divider
        }
        scale={selected ? 1.5 : 1}
      >
        <PartnerAvatar
          partnerId={job.merchant.id}
          sx={
            selected
              ? {
                  width: 24,
                  height: 24,
                  backgroundColor: theme.palette.background.paper,
                }
              : { width: 20, height: 20 }
          }
        />
      </Pin>
    </AdvancedMarker>
  );
}
