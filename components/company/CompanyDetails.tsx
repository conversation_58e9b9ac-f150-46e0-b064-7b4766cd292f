import CompanyCertificates from "./CompanyCertificates";
import CompanyFixedPriceJobs from "./CompanyFixedPriceJobs";
import CompanyJobs from "./CompanyJobs";
import CompanyServices from "./CompanyServices";
import CompanyStatusCard from "./CompanyStatusCard";
import CompanyTags from "./CompanyTags";
import CompanyUsers from "./CompanyUsers";
import CompanyVetting from "./CompanyVetting";
import WriteCompanyDialog from "./WriteCompanyDialog";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import Loading from "../common/Loading";
import OfferedJobsTable from "../job/OfferedJobsTable";
import TextAreaEditor from "../common/TextAreaEditor";
import UploadImageButton from "../common/UploadImageButton";
import WhatshotIcon from "@mui/icons-material/Whatshot";
import { doc, getFirestore } from "@firebase/firestore";
import { CompanyQuoteDefaultSettings } from "./CompanyQuoteDefaultSettings";
import { formatTime } from "../../lib/format-time-relative";
import { useCompanyDocument } from "../../models/company/company";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Card,
  Typography,
  Stack,
  Alert,
  Divider,
  IconButton,
  AlertTitle,
} from "@mui/material";
import { FIREBASE_URL } from "../../config/constants";
import CompanyInsuredByCard from "./CompanyInsuredByCard";
import CompanySocialLinksEditor from "./CompanySocialLinksEditor";
import CompanyHomeLocation from "./CompanyHomeLocation";
import PageContainer from "../layout/PageContainer";
import CompanyDocuments from "./CompanyDocuments";
import DialogManager from "../dialogs/DialogManager";
import ManagedDialog from "../dialogs/ManagedDialog";
import UploadDocumentDialog from "./dialogs/UploadDocumentDialog";
import ComponentContainer from "../common/ComponentContainer";
import { DocumentDropArea, EditCompanyButton } from "./Company";
import CompanyRatings from "./CompanyRatings";
import InfoItemRow from "../common/InfoItemRow";
import SubjectInternalCommentsSection from "../job/internal-comments/SubjectInternalCommentsSection";
import { SdCardAlert } from "@mui/icons-material";
import { startCase } from "lodash";
import FileQueue from "../upload/FileQueue";
import ReviewWidgetTools from "./CompanyReviewWidgetTools";
import Internal from "../auth/Internal";
import { DonePartnerId } from "../../models/other/partner";
import { useAuthUser } from "../auth/AuthProvider";
import { Visible } from "../common/Conditional";
import { CompanyNetworks } from "./CompanyNetworks";
import CompanyAvatar from "./CompanyAvatar";

interface CompanyDetailsProps {
  companyId: string;
  hideInternalComments?: boolean;
}

export default function CompanyDetails({
  companyId,
  hideInternalComments = false,
}: CompanyDetailsProps) {
  const { userDoc, partnerDoc } = useAuthUser();
  const companyRef = doc(getFirestore(), `companies/${companyId}`);
  const { company, loading } = useCompanyDocument(companyRef.id);

  if (loading) return <Loading />;
  if (!company) return null;

  const isSuperUser = userDoc?.isSuperUser ?? false;
  const isDoneInstaller = company.memberOf.includes(DonePartnerId);
  const showEditControls = !isDoneInstaller || isSuperUser;

  return (
    <FileQueue>
      <DialogManager>
        <DocumentDropArea company={company}>
          <PageContainer tight>
            <Visible if={showEditControls}>
              <ManagedDialog
                id={"upload-document"}
                component={UploadDocumentDialog}
              />

              <ManagedDialog
                id="edit-company"
                company={company}
                component={WriteCompanyDialog}
              />
            </Visible>
            <Card variant="outlined" square sx={{ padding: 2, margin: 0.5 }}>
              <Stack spacing={2}>
                <Stack alignItems="center" direction="row" spacing={1}>
                  <CompanyAvatar
                    company={company}
                    sx={{ width: 40, height: 40 }}
                  />
                  <Typography variant="h5">{company.name}</Typography>
                  <CompanyNetworks
                    company={company}
                    visibleNetworks={partnerDoc?.networks}
                  />
                </Stack>

                <Visible if={showEditControls}>
                  <Stack alignItems="center" direction="row" spacing={1}>
                    <EditCompanyButton />

                    <UploadImageButton
                      documentRef={companyRef}
                      dataKey="logoRef"
                      fileKey={`company-profile-images-v1/${companyId}/original.png`}
                    />
                    <Internal>
                      <a
                        target="_blank"
                        rel="noopener noreferrer"
                        href={`${FIREBASE_URL}/firestore/data/~2Fcompanies~2F${companyId}`}
                      >
                        <IconButton color="warning" size="small">
                          <WhatshotIcon />
                        </IconButton>
                      </a>
                    </Internal>
                  </Stack>
                </Visible>
              </Stack>
            </Card>

            <ComponentContainer title="">
              <Stack spacing={2}>
                <Internal>
                  {company.checkStatus === "failed" && (
                    <Alert severity="error">
                      <Stack spacing={1}>
                        <AlertTitle>Company checks failed</AlertTitle>

                        {company.checks &&
                          Object.entries(company.checks)
                            .filter(([_, value]) => value === false)
                            .map(([key, _]) => {
                              return (
                                <Stack direction="row" spacing={1} key={key}>
                                  <SdCardAlert color="error" />{" "}
                                  <Typography color="error.main">
                                    {startCase(key)} is missing!
                                  </Typography>
                                </Stack>
                              );
                            })}
                      </Stack>
                    </Alert>
                  )}
                </Internal>

                <Card variant="outlined" style={{ padding: 12 }}>
                  <Stack spacing={1} divider={<Divider flexItem />}>
                    <InfoItemRow title="Legal name" value={company.legalName} />

                    <InfoItemRow
                      title="Organisation number"
                      value={company.orgNo}
                    />
                    <Internal>
                      <InfoItemRow
                        title="Bankgiro"
                        value={company?.billingSettings?.paymentInfo?.bankgiro}
                      />
                      <InfoItemRow
                        title="Bank account"
                        value={
                          company?.billingSettings?.paymentInfo?.bankAccount
                        }
                      />
                    </Internal>

                    <Visible if={showEditControls}>
                      <InfoItemRow
                        title="Last offer reply time"
                        noCopy
                        value={
                          formatTime(
                            company.statistics?.lastOfferReplyTime?.toDate(),
                          ) ?? "Not reply yet"
                        }
                      />

                      <InfoItemRow
                        title="Completed jobs"
                        noCopy
                        value={(
                          company.statistics?.numberOfCompletedJobs ?? 0
                        ).toString()}
                      />
                    </Visible>
                  </Stack>
                </Card>
                <Visible if={showEditControls}>
                  <Card variant="outlined" style={{ padding: 12 }}>
                    <Stack spacing={2}>
                      <CompanyRatings companyRef={companyRef} />

                      <Internal>
                        {company.statistics?.numberOfReviews && (
                          <ReviewWidgetTools companyId={companyId} />
                        )}
                      </Internal>
                    </Stack>
                  </Card>

                  <CompanyStatusCard company={company} />
                  <CompanyInsuredByCard company={company} />
                </Visible>
                <Internal>
                  <Stack spacing={0.5}>
                    <Typography variant="caption">Tags</Typography>
                    <CompanyTags company={company} />
                  </Stack>

                  <Stack spacing={0.5}>
                    <Typography variant="caption">Services</Typography>
                    <CompanyServices company={company} />
                  </Stack>
                </Internal>
                <Visible if={showEditControls}>
                  <Stack spacing={0.5}>
                    <CompanyHomeLocation company={company} />
                  </Stack>
                </Visible>
              </Stack>
            </ComponentContainer>

            <ComponentContainer>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>Users</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <CompanyUsers
                    company={company}
                    showEditControls={showEditControls}
                  />
                </AccordionDetails>
              </Accordion>
              <Visible if={showEditControls}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>Profile</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography>Profile text</Typography>
                    <div style={{ height: 12 }} />
                    <TextAreaEditor docRef={companyRef} dataKey="profileText" />
                    <br></br>
                    <CompanySocialLinksEditor company={company} />
                  </AccordionDetails>
                </Accordion>
              </Visible>
              <Internal>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>Fixed price jobs</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <CompanyFixedPriceJobs company={company} />
                  </AccordionDetails>
                </Accordion>

                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>Vetting</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <CompanyVetting company={company} companyRef={companyRef} />
                  </AccordionDetails>
                </Accordion>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>Certificates</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <CompanyCertificates company={company} />
                  </AccordionDetails>
                </Accordion>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>Documents</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <CompanyDocuments companyId={company.reference.id} />
                  </AccordionDetails>
                </Accordion>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>Default quote settings</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <CompanyQuoteDefaultSettings companyRef={companyRef} />
                  </AccordionDetails>
                </Accordion>
              </Internal>
            </ComponentContainer>

            <ComponentContainer
              title="Company internal comments"
              hidden={hideInternalComments}
            >
              <SubjectInternalCommentsSection subject={company.reference} />
            </ComponentContainer>

            <Internal>
              <ComponentContainer title="Company jobs">
                <CompanyJobs companyRef={companyRef} />
              </ComponentContainer>
              <ComponentContainer title="Offered jobs">
                <OfferedJobsTable companyReference={companyRef} />
              </ComponentContainer>
            </Internal>
          </PageContainer>
        </DocumentDropArea>
      </DialogManager>
    </FileQueue>
  );
}
