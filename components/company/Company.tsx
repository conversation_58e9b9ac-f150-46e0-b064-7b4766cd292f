import React from "react";
import { Company } from "../../models/company/company";
import { Button } from "@mui/material";
import { useDialog } from "../dialogs/DialogManager";
import FileDropArea from "../upload/FileDropArea";
import { Edit } from "@mui/icons-material";

export function DocumentDropArea({
  children,
  company,
}: {
  children: React.ReactNode;
  company: Company;
}) {
  const { open: openUploadDocuments } = useDialog("upload-document");

  return (
    <FileDropArea
      onDrop={(files) => {
        if (!files || files.length === 0) return;
        openUploadDocuments({ files, company });
      }}
    >
      {children}
    </FileDropArea>
  );
}

export function EditCompanyButton() {
  const { open } = useDialog("edit-company");

  return (
    <Button
      size="small"
      variant="outlined"
      startIcon={<Edit />}
      onClick={() => {
        open();
      }}
    >
      Edit
    </Button>
  );
}
