import { Chip, ChipProps } from "@mui/material";
import DoneIcon from "../common/DoneIcon";
import { partnerRef } from "../../models/other/partner";
import PartnerAvatar from "../partners/PartnerAvatar";
import { memo } from "react";
import { useMemoDocument } from "../../lib/hooks/use-memo-document";

interface NetworkChipProps
  extends Omit<ChipProps, "icon" | "color" | "label" | "variant"> {
  network: string;
}

/**
 * A chip that shows a network.
 */
const NetworkChip = memo(function NetworkChip({
  network,
  size = "small",
  ...props
}: NetworkChipProps) {
  const [partner] = useMemoDocument(partnerRef(network));
  return (
    <Chip
      key={network}
      size={size}
      label={partner?.displayName ?? partner?.name ?? network}
      variant="outlined"
      icon={network === "done" ? <DoneIcon /> : undefined}
      avatar={
        network !== "done" ? <PartnerAvatar partnerId={network} /> : undefined
      }
      color={network === "done" ? "primary" : undefined}
      {...props}
    />
  );
});

export default NetworkChip;
