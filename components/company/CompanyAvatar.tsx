import { Avatar, AvatarProps } from "@mui/material";
import { useStorageURL } from "../../lib/hooks/use-storage-url";
import { Company } from "../../models/company/company";
import uniqolor from "uniqolor";
import _ from "lodash";

interface CompanyAvatarProps extends Omit<AvatarProps, "src"> {
  company: Company;
}

const colorForCompany = _.memoize((id: string) => uniqolor(id).color);

export default function CompanyAvatar({
  company,
  ...props
}: CompanyAvatarProps) {
  const [fileUrl] = useStorageURL(company.logoRef);

  if (fileUrl) {
    return <Avatar {...props} src={fileUrl} />;
  }
  return (
    <Avatar
      {...props}
      sx={{ bgcolor: colorForCompany(company.reference.id), ...props.sx }}
    >
      {company.name[0]}
    </Avatar>
  );
}
