import { useMemo } from "react";

import {
  Avatar,
  AvatarProps,
  Tooltip,
  Badge,
  capitalize,
  styled,
} from "@mui/material";
import { DocumentReference } from "@firebase/firestore";
import { User } from "../../models/user/user";
import { isDefined } from "../../lib/filter-undefined";
import { useStorageURL } from "../../lib/hooks/use-storage-url";
import { useMemoDocument } from "../../lib/hooks/use-memo-document";
import { partnerRef } from "../../models/other/partner";
import { isModel } from "../../lib/filter-is-model";

const getInitials = (user: User) => {
  if (user && user.firstName) {
    const lastName = user.lastName || " ";
    return `${user.firstName[0]}${lastName[0]}`;
  }
  return "-";
};

interface UserAvatarWithImagesProps extends AvatarProps {
  fileRef?: string;
  initials: string;
}

function UserAvatarWithImage(props: UserAvatarWithImagesProps) {
  const { fileRef, initials, ...avatarProps } = props;
  const [fileUrl] = useStorageURL(fileRef);

  if (fileUrl) {
    return <Avatar {...avatarProps} src={fileUrl} />;
  }
  return <Avatar {...avatarProps}>{initials}</Avatar>;
}

export interface UserAvatarProps extends AvatarProps {
  userRef: DocumentReference;
  size?: "tiny" | "small" | "medium";
}

function sizeToStyle(size: UserAvatarProps["size"]) {
  switch (size) {
    case "tiny":
      return {
        width: 16,
        height: 16,
        fontSize: "0.5rem",
      };
    case "small":
      return {
        width: 24,
        height: 24,
        fontSize: "0.8rem",
      };
    default:
      return {
        width: 36,
        height: 36,
        fontSize: "1rem",
      };
  }
}

export default function UserAvatar(props: UserAvatarProps) {
  const { userRef, size = "medium", sx = {}, ...avatarProps } = props;
  const [user] = useMemoDocument(userRef);

  const style = { ...sizeToStyle(size), ...sx };

  if (!user || !isModel<User>("users", user))
    return <Avatar {...avatarProps} sx={style} />;

  const initials = getInitials(user);

  return (
    <Tooltip
      arrow
      title={[
        user.firstName,
        user.lastName,
        user.partner?.id && `- ${capitalize(user.partner?.id)}`,
      ]
        .filter(isDefined)
        .join(" ")}
    >
      <Badge
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        badgeContent={
          user.partner ? (
            <PartnerBadge partnerId={user.partner.id} size={size} />
          ) : null
        }
      >
        <UserAvatarWithImage
          {...avatarProps}
          sx={style}
          initials={initials}
          fileRef={user.profileImage}
        />
      </Badge>
    </Tooltip>
  );
}

function badgeSizeToStyle(size: UserAvatarProps["size"]) {
  switch (size) {
    case "tiny":
      return {
        width: 15,
        height: 15,
      };
    case "small":
      return {
        width: 18,
        height: 18,
      };
    default:
      return {
        width: 36,
        height: 36,
      };
  }
}

const SmallAvatar = styled(Avatar)(({ theme }) => ({
  width: 22,
  height: 22,
  border: `2px solid ${theme.palette.background.paper}`,
  backgroundColor: theme.palette.background.paper,
}));

function PartnerBadge({
  partnerId,
  size,
}: {
  partnerId: string;
  size: UserAvatarProps["size"];
}) {
  const imageSize = useMemo(() => badgeSizeToStyle(size), [size]);
  const partnerDocRef = useMemo(() => partnerRef(partnerId), [partnerId]);
  const [partner] = useMemoDocument(partnerDocRef);

  const [src] = useStorageURL(partner?.smallLogo);

  if (!src) return null;

  return <SmallAvatar sx={imageSize} src={src} />;
}
