import { partnerRef } from "../../models/other/partner";
import { Avatar, AvatarProps } from "@mui/material";
import { useMemoDocument } from "../../lib/hooks/use-memo-document";
import { memo } from "react";

interface PartnerAvatarProps extends Omit<AvatarProps, "src"> {
  partnerId: string;
  size?: number;
}

const PartnerAvatar = memo(function PartnerAvatar({
  partnerId,
  ...props
}: PartnerAvatarProps) {
  const [partner] = useMemoDocument(partnerRef(partnerId));
  return <Avatar src={partner?.smallLogo} {...props} />;
})

export default PartnerAvatar
