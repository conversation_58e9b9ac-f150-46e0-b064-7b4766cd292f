import { Verified } from "@mui/icons-material";
import { Stack } from "@mui/system";
import { GridCellParams } from "@mui/x-data-grid";
import { isAfter } from "date-fns";
import { useMemo } from "react";
import { isDefined } from "../../../lib/filter-undefined";
import { formatTime } from "../../../lib/format-time-relative";
import { Job, JobStatus } from "../../../models/job/job";
import { generateRowForJob } from "../../../models/job/job-row";
import {
  ExtendedGridColDef,
  relativeDateTimeColumn,
  stringColumn,
  optional,
  dateTimeColumn,
} from "../../common/table/columns";
import { PlainDataGridBase } from "../../common/table/PlainDataGrid";
import JobEventsProgress from "../JobEventsProgress";
import { colorForScheduledDate } from "../table/lib/color-for-date";
import Communication from "../table/lib/Communication";
import OfferChip from "../table/lib/OfferChip";
import TicketInfo from "../table/lib/TicketInfo";
import { Chip, Tooltip } from "@mui/material";

export default function JobsDataGrid({
  jobs,
  onSelected,
  configuration,
}: {
  jobs?: Job[];
  selectedJob?: Job;
  onSelected?: (id: string) => void;
  configuration: ColumnConfiguration;
}) {
  const rows = useMemo(() => jobs?.map(generateRowForJob) ?? [], [jobs]);

  return (
    <PlainDataGridBase
      density="compact"
      rows={rows}
      columns={columns(configuration)}
      sx={{ border: "none" }}
      loading={false}
      onRowSelected={(id) => onSelected?.(id)}
    />
  );
}

function StatusChip({ status }: { status: JobStatus }) {
  switch (status) {
    case "open":
      return (
        <Chip label={status} size="small" variant="outlined" color="success" />
      );
    case "inbox":
      return (
        <Chip label={status} size="small" variant="outlined" color="warning" />
      );
    default:
      return (
        <Chip label={status} size="small" variant="outlined" color="default" />
      );
  }
}

export type ColumnConfiguration = "merchant" | "network";

const columns = (config: ColumnConfiguration): ExtendedGridColDef[] => {
  const columns = [
    relativeDateTimeColumn({
      field: "createTime",
      headerName: "Created",
      width: 150,
    }),
    stringColumn({
      field: "status",
      headerName: "Status",
      renderCell: (params: GridCellParams) => {
        return <StatusChip status={params.row.data.status} />;
      },
    }),
    stringColumn({
      headerName: "Event",
      disableExport: true,
      sortable: false,
      field: "events",
      width: 60,
      renderCell: (params: GridCellParams) => {
        return <JobEventsProgress job={params.row.data} showOnlyLatest />;
      },
    }),
    dateTimeColumn({
      headerName: "Chat",
      disableExport: true,
      sortable: true,
      field: "lastCommunicatedTime",
      width: 250,
      renderCell: (params: GridCellParams) => {
        return (
          <Communication
            date={params.row.data.lastCommunicatedTime}
            message={params.row.data.lastChatMessage}
          />
        );
      },
    }),
    relativeDateTimeColumn({
      field: "scheduledWorkStartTime",
      headerName: "Scheduled",
      width: 150,
      color: colorForScheduledDate,
    }),
    stringColumn({
      headerName: "Extras",
      disableExport: true,
      width: 145,
      sortComparator: (a, b) => {
        return (b?.date?.getTime() ?? 0) - (a?.date?.getTime() ?? 0);
      },
      field: "offerStatus",
      renderCell: ({ row, value }) => {
        const isChecked = Boolean(row.offersCheckedAt);
        const status = value?.status ?? (isChecked ? "noOffer" : null);
        return (
          <Stack direction="row" alignItems="center" spacing={1}>
            {isChecked && (
              <Tooltip
                title={`Extras verified ${formatTime(row.offersCheckedAt)}`}
                arrow
              >
                <Verified
                  color={
                    !row.offerSentAt ||
                    isAfter(row.offersCheckedAt, row.offerSentAt)
                      ? "primary"
                      : "warning"
                  }
                  fontSize="small"
                />
              </Tooltip>
            )}
            {status && <OfferChip status={status} date={value?.date} />}
          </Stack>
        );
      },
    }),
    stringColumn({
      field: "Inbox",
      headerName: "Inbox",
      sortable: false,
      width: 250,
      renderCell: (params: GridCellParams) => {
        return (
          <TicketInfo
            subjectRef={params.row.data.reference}
            participant="done"
          />
        );
      },
    }),
    stringColumn({
      field: "companyName",
      headerName: "Installer",
      width: 220,
    }),
    optional(
      config === "merchant",
      stringColumn({
        field: "partner",
        headerName: "Partner",
        width: 160,
      }),
    ),
    optional(
      config === "network",
      stringColumn({
        field: "network",
        headerName: "Network",
        width: 160,
      }),
    ),
    stringColumn({
      field: "location",
      headerName: "Location",
      width: 160,
    }),
    stringColumn({
      field: "customerName",
      headerName: "Customer",
      width: 220,
    }),
  ];
  return columns.filter(isDefined);
};
