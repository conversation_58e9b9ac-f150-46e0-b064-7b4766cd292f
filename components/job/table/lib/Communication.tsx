import { Chat } from "@mui/icons-material";
import { isOlderThanDays } from "../../../../lib/date-compare";
import { DateOrTimestamp } from "../../../../lib/date-or-timestamp";
import { Chip, Stack, Tooltip, Typography } from "@mui/material";
import formatTimeRelative, {
  formatTinyDistance,
} from "../../../../lib/format-time-relative";

const isOld = isOlderThanDays(3, false);
const isVeryOld = isOlderThanDays(7, false);

const colorForDate = (value: DateOrTimestamp) => {
  if (!value) return "default";
  if (isVeryOld(value)) return "error";
  if (isOld(value)) return "warning";
  return "primary";
};

/**
 * An icon that displays the last communication time of a job.
 */
export default function Communication({
  date,
  message,
}: {
  date: Date;
  message?: string;
}) {
  return (
    <Tooltip
      title={
        <Stack>
          <Typography variant="body2">{message}</Typography>
          <Typography variant="caption">{formatTimeRelative(date)}</Typography>
        </Stack>
      }
      arrow
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={0.5}
        height={"100%"}
        width={"100%"}
      >
        <Chip
          label={formatTinyDistance(date)}
          size="small"
          variant="outlined"
          color={colorForDate(date)}
          icon={<Chat fontSize="inherit" />}
        />
        <Typography variant="body2" noWrap>
          {message}
        </Typography>
      </Stack>
    </Tooltip>
  );
}
