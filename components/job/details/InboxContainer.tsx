import { Divider } from "@mui/material";
import { DocumentReference, doc, getFirestore } from "firebase/firestore";
import { useMemo, useState } from "react";
import SubjectInternalCommentsSection from "../internal-comments/SubjectInternalCommentsSection";
import SelectButton, { SelectButtonItem } from "../../menu/SelectButton";
import ComponentContainer from "../../common/ComponentContainer";
import UserSelectButtonItem from "../../menu/UserSelectButtonItem";
import { useMemoDocument } from "../../../lib/hooks/use-memo-document";
import { User } from "../../../models/user/user";
import { Job } from "../../../models/job/job";
import { Company } from "../../../models/company/company";
import { useDocumentDataOnce } from "react-firebase-hooks/firestore";
import { isModel } from "../../../lib/filter-is-model";
import { upperFirst } from "lodash";
import { PlatformTier } from "../../auth/Tiered";
import DialogManager from "../../dialogs/DialogManager";
import ManagedDialog from "../../dialogs/ManagedDialog";
import ConfirmDeleteCommentDialog from "../../../models/inbox/dialogs/ConfirmDeleteComment";

interface InboxContainerProps {
  job: DocumentReference;
  customer?: DocumentReference<User>;
  company?: DocumentReference<Company>;
}

export default function InboxContainer({
  job,
  customer,
  company,
}: InboxContainerProps) {
  const [subject, setSubject] = useState<DocumentReference>(job);
  const [model] = useMemoDocument(subject);

  const title = useMemo(() => {
    if (isModel<User>("users", model)) {
      return `${model.firstName} ${model.lastName}`;
    }
    if (isModel<Job>("jobs", model)) {
      return "Order";
    }
    if (isModel<Company>("companies", model)) {
      return model.name;
    }
    return "Inbox";
  }, [model]);

  return (
    <DialogManager>
      <ManagedDialog
        id="confirm-delete-comment"
        component={ConfirmDeleteCommentDialog}
      />
      <ComponentContainer title="Inbox">
        <SubjectInternalCommentsSection
          subject={subject}
          tools={
            <PlatformTier>
              <SelectButton
                persistent
                title={title}
                value={subject.path}
                onSelection={(item) =>
                  setSubject(doc(getFirestore(), item as string))
                }
              >
                <SelectButtonItem value={job.path}>Order</SelectButtonItem>
                {customer ? (
                  <UserSelectButtonItem
                    userRef={customer}
                    value={customer.path}
                    label="Customer"
                  />
                ) : null}
                <Divider />
                {company ? <CompanyMenuItems companyRef={company} /> : null}
              </SelectButton>
            </PlatformTier>
          }
        />
      </ComponentContainer>
    </DialogManager>
  );
}

function CompanyMenuItems({
  companyRef,
}: {
  companyRef: DocumentReference<Company>;
}) {
  const [company] = useDocumentDataOnce(companyRef);
  if (!company) return null;
  return (
    <>
      <SelectButtonItem value={companyRef.path}>
        {company.name}
      </SelectButtonItem>
      {Object.values(company.usersV2).map(({ reference, role }) => (
        <UserSelectButtonItem
          key={reference.path}
          userRef={reference}
          value={reference.path}
          label={upperFirst(role)}
        />
      ))}
    </>
  );
}
