import { v4 as uuid } from "uuid";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>vider, Grid2, Typo<PERSON> } from "@mui/material";
import { LinesDataGridField, Summary } from "../invoice/editing";
import { InvoiceLineItem } from "../../models/invoice/invoice-line-item";
import {
  ArticlePriceType,
  isCentAmount,
  isPercentageAmount,
} from "../../models/article-groups/article";
import { calculatedValuesForLines } from "../../models/quote/calculations";
import { CompletedPartnerInstallation } from "../../models/installation-report/installation-report";
import Internal from "../auth/Internal";
import { PlatformTier } from "../auth/Tiered";
import { updateLines } from "../../models/installation-report/provider";
import { Job } from "../../models/job/job";
import { useMemoDocument } from "../../lib/hooks/use-memo-document";
import { partnerRef } from "../../models/other/partner";
import {
  LineItemArticle,
  RawQuoteLineItem,
} from "../../models/quote/raw-quote-line-item";
import { CentValue, toCents, toNumber } from "../../models/finance/cent-value";

interface InstallationInvoiceReportProps {
  articleGroupId?: string;
  job: Job;
  installationReport: CompletedPartnerInstallation;
}

export default function InstallationInvoiceReport({
  articleGroupId,
  job,
  installationReport,
}: InstallationInvoiceReportProps) {
  const lines =
    installationReport?.lines?.map<InvoiceLineItem>((line) => ({
      uid: line.uid ?? uuid(),
      workedHours: null,
      detailedDeductionType: null,
      ...line,
    })) ?? [];

  const [partner] = useMemoDocument(partnerRef(job.partner));

  return (
    <Grid2 container spacing={1}>
      <Internal>
        <PartnerSummary
          lines={lines}
          title={partner?.displayName ?? "Partner"}
        />
      </Internal>
      <PlatformTier exact>
        <CustomerSummary
          lines={lines}
          title={job.cache?.customerName ?? "Customer"}
        />
      </PlatformTier>
      <InstallerSummary
        lines={lines}
        title={job.cache?.companyName ?? "Installer"}
      />
      <Grid2 size={12}>
        <LinesDataGridField
          hideFooter
          editable
          articleGroupId={articleGroupId}
          lines={lines}
          mode="customer"
          onUpdate={async (newLines) => {
            await updateLines(installationReport.reference.id, newLines);
          }}
        />
      </Grid2>
    </Grid2>
  );
}

export function PartnerSummary({
  lines,
  title,
}: {
  lines: RawQuoteLineItem[];
  title: string;
}) {
  const installerLines = lines.map((line) => ({
    ...line,
    deductionType: null,
    unitPrice: toNumber(
      getUnitAmountValue(line.article, "partnerPrice", toCents(line.unitPrice)),
    ),
  }));

  const calculatedValues = calculatedValuesForLines(installerLines, {
    includeVat: false,
    includeDeduction: false,
  });
  return (
    <SummaryContainer title={title}>
      <Summary calculatedValues={calculatedValues} />
    </SummaryContainer>
  );
}

export function CustomerSummary({
  lines,
  title,
}: {
  lines: RawQuoteLineItem[];
  title: string;
}) {
  const calculatedValues = calculatedValuesForLines(lines);
  return (
    <SummaryContainer title={title}>
      <Summary calculatedValues={calculatedValues} />
    </SummaryContainer>
  );
}

export function InstallerSummary({
  lines,
  title,
}: {
  lines: RawQuoteLineItem[];
  title: string;
}) {
  const installerLines = lines.map((line) => ({
    ...line,
    deductionType: null,
    unitPrice: toNumber(
      getUnitAmountValue(
        line.article,
        "craftsmanProceeds",
        toCents(line.unitPrice),
      ),
    ),
  }));

  const calculatedValues = calculatedValuesForLines(installerLines, {
    includeVat: false,
    includeDeduction: false,
  });
  return (
    <SummaryContainer title={title}>
      <Summary calculatedValues={calculatedValues} />
    </SummaryContainer>
  );
}

export function SummaryContainer({
  children,
  title,
}: {
  title: string;
  children: React.ReactNode;
}) {
  return (
    <Card variant="outlined">
      <CardContent sx={{ padding: 1 }}>
        <Typography variant="body1">{title}</Typography>
      </CardContent>
      <Divider />
      <CardContent sx={{ padding: 1 }}>{children}</CardContent>
    </Card>
  );
}

// FIXME: Taken from done-platform, remove when this project moves there...
function getUnitAmountValue(
  article: LineItemArticle | undefined,
  price: ArticlePriceType,
  unitPrice: CentValue,
): CentValue {
  if (!article) {
    return unitPrice;
  }

  const priceInfo = article.prices[price];
  if (!priceInfo)
    throw Error(
      `No amount info for article ${
        article.reference?.path ?? "Custom article"
      } in configuration ${price}`,
    );

  if (isCentAmount(priceInfo)) {
    return priceInfo.amount.value;
  }

  if (isPercentageAmount(priceInfo)) {
    return Math.round(unitPrice * priceInfo.amount.percentage);
  }

  throw Error(
    `Percentage or unit price is missing for ${
      article.reference?.path ?? "Custom article"
    } in configuration ${price}`,
  );
}
