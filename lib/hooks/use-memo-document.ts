import { useEffect, useState } from "react";
import pMemoize from "p-memoize";
import { DocumentData, DocumentReference, getDoc } from "firebase/firestore";

async function documentLoader<T>(ref?: DocumentReference<T>) {
  if (!ref) {
    return undefined;
  }
  const snapshot = await getDoc(ref);
  if (snapshot.exists()) {
    return snapshot.data();
  } else {
    return undefined;
  }
}

const loader = pMemoize(documentLoader, { cacheKey: ([ref]) => ref?.path });

/**
 * Loads a document once and memoizes the result.
 * @param ref Reference to the document to load.
 * @returns The document, loading state and error.
 */
export function useMemoDocument<T = DocumentData>(ref?: DocumentReference<T>) {
  const [document, setDocument] = useState<T>();
  const [loading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error>();

  useEffect(() => {
    loader(ref)
      .then((data) => {
        setDocument(data);
        setIsLoading(false);
      })
      .catch((error) => {
        setError(error);
        setIsLoading(false);
        setDocument(undefined);
      });
  }, [ref]);

  return [document, loading, error] as const;
}
