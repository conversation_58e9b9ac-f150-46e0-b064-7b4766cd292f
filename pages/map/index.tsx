import { query, where, orderBy} from "firebase/firestore";
import { useMemo } from "react";
import { useCollectionDataOnce } from "react-firebase-hooks/firestore";
import OperationsMap from "../../components/map/OperationsMap";
import { jobsCollection } from "../../models/job/job-provider";
import { partnerRef } from "../../models/other/partner";

export default function MapPage() {
  const jobsQuery = useMemo(() => {
    return query(
      jobsCollection(),
      where("merchant", "==", partnerRef("elbilsvaruhuset")),
      where("status", "in", ["open", "inbox", "closed"]),
      orderBy("createTime", "desc")
    );
  }, []);

  const [jobs, loading, error] = useCollectionDataOnce(jobsQuery);

  console.log(loading, error)

  return <OperationsMap jobs={jobs} />;
}
